"use client";

import { useEffect, useRef } from "react";
import { useLanguage } from "./language-provider";

export function LanguageCacheDetector() {
  const { language } = useLanguage();
  const previousLanguageRef = useRef<string | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Check if PWA is installed
    const isStandalone = window.matchMedia("(display-mode: standalone)").matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    const isPWAInstalled = isStandalone || isInWebAppiOS;

    if (!isPWAInstalled) {
      console.log('[Language Cache Detector] PWA not installed, skipping');
      return;
    }

    // Check if language has changed
    const previousLanguage = previousLanguageRef.current;
    
    if (previousLanguage && previousLanguage !== language) {
      console.log(`[Language Cache Detector] Language changed from ${previousLanguage} to ${language}`);
      
      // Notify service worker about language change
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.active?.postMessage({
            type: 'LANGUAGE_CHANGED',
            newLang: language,
            oldLang: previousLanguage
          });
        });
      }
    } else if (!previousLanguage) {
      // First time, cache current language
      console.log(`[Language Cache Detector] Initial language: ${language}`);
      
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.active?.postMessage({
            type: 'CACHE_LANGUAGE',
            lang: language
          });
        });
      }
    }

    // Update previous language reference
    previousLanguageRef.current = language;
  }, [language]);

  return null; // This component doesn't render anything
}
