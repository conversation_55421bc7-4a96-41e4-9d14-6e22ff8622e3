"use client";

import { useState } from "react";
import { useLanguage } from "./language-provider";

export function ForceCacheButton() {
  const { language } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<string>("");

  const forceCache = async () => {
    setIsLoading(true);
    setStatus("Starting cache process...");

    try {
      // Check if service worker is available
      if (!('serviceWorker' in navigator)) {
        setStatus("❌ Service Worker not supported");
        setIsLoading(false);
        return;
      }

      setStatus("🔄 Updating Service Worker...");

      // Force update service worker
      const registration = await navigator.serviceWorker.register('/sw.js', {
        updateViaCache: 'none'
      });

      if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      }

      await navigator.serviceWorker.ready;

      if (!registration.active) {
        setStatus("❌ Service Worker not active");
        setIsLoading(false);
        return;
      }

      setStatus("✅ Service Worker ready, testing communication...");

      // Test communication first
      registration.active.postMessage({ type: 'TEST' });

      await new Promise(resolve => setTimeout(resolve, 1000));

      setStatus("✅ Communication OK, forcing PWA installation...");

      // Force mark PWA as installed
      registration.active.postMessage({
        type: 'PWA_INSTALLED'
      });

      await new Promise(resolve => setTimeout(resolve, 1000));

      setStatus(`🔄 Forcing cache for language: ${language}`);

      // Force cache current language
      registration.active.postMessage({
        type: 'CACHE_LANGUAGE',
        lang: language
      });

      await new Promise(resolve => setTimeout(resolve, 3000));

      setStatus("🔄 Forcing cache for English as fallback...");

      // Also cache English as fallback
      if (language !== 'en') {
        registration.active.postMessage({
          type: 'CACHE_LANGUAGE',
          lang: 'en'
        });

        await new Promise(resolve => setTimeout(resolve, 3000));
      }

      // Check cache status
      const cache = await caches.open('timerkit-cache-v9');
      const cachedRequests = await cache.keys();
      const totalCached = cachedRequests.length;

      setStatus(`✅ Cache completed! ${totalCached} items cached`);

      // Show cached URLs
      console.log('Cached URLs:', cachedRequests.map(req => req.url));

    } catch (error) {
      console.error('Force cache error:', error);
      setStatus(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    setIsLoading(false);
  };

  const clearCache = async () => {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(cacheNames.map(name => caches.delete(name)));
      setStatus("🗑️ Cache cleared");
    } catch (error) {
      setStatus(`❌ Clear error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const checkCache = async () => {
    try {
      const cache = await caches.open('timerkit-cache-v9');
      const cachedRequests = await cache.keys();
      const urls = cachedRequests.map(req => req.url);
      
      setStatus(`📊 Cache contains ${urls.length} items`);
      console.log('Current cache:', urls);
    } catch (error) {
      setStatus(`❌ Check error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Only show in development or when explicitly enabled
  const isDev = process.env.NODE_ENV === 'development';
  const showButton = isDev || localStorage.getItem('showForceCache') === 'true';

  if (!showButton) return null;

  return (
    <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg max-w-sm">
      <h3 className="font-semibold mb-3 text-sm">Force Cache ({language})</h3>
      
      <div className="space-y-2">
        <button
          onClick={forceCache}
          disabled={isLoading}
          className="w-full px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? '🔄 Caching...' : '🚀 Force Cache'}
        </button>
        
        <div className="flex gap-2">
          <button
            onClick={checkCache}
            className="flex-1 px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600"
          >
            📊 Check
          </button>
          
          <button
            onClick={clearCache}
            className="flex-1 px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
          >
            🗑️ Clear
          </button>
        </div>
      </div>
      
      {status && (
        <div className="mt-3 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">
          {status}
        </div>
      )}
    </div>
  );
}

// Function to enable the button in production
// Run in console: enableForceCache()
if (typeof window !== 'undefined') {
  (window as any).enableForceCache = () => {
    localStorage.setItem('showForceCache', 'true');
    window.location.reload();
  };
}
