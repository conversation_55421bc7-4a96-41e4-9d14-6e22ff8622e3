// Service Worker for Timer Kit PWA
const CACHE_NAME = 'timerkit-cache-v9';

// Flag to track if PWA is installed
let isPWAInstalled = false;

// Set to track cached languages
let cachedLanguages = new Set();

// Core assets to cache on install (minimal set)
const PRECACHE_ASSETS = [
  '/offline.html',
  '/manifest.json',
  '/favicon.ico',
  '/logo.png',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/apple-touch-icon.png',
  '/icons/favicon-96x96.png',
  '/sound/bell.mp3',
  '/workers/countdown-worker.js',
  '/workers/stopwatch-worker.js',
  '/workers/simple-stopwatch-worker.js',
  '/workers/time-tracking-worker.js'
];

// Function to check if PWA is installed
async function checkPWAInstallation() {
  try {
    // Check if we have a stored flag
    const cache = await caches.open(CACHE_NAME);
    const installFlag = await cache.match('/pwa-installed-flag');
    if (installFlag) {
      isPWAInstalled = true;
      console.log('[Service Worker] PWA installation flag found');
    }

    // Load cached languages list
    const cachedLangsResponse = await cache.match('/cached-languages-list');
    if (cachedLangsResponse) {
      const cachedLangsText = await cachedLangsResponse.text();
      const langs = JSON.parse(cachedLangsText);
      cachedLanguages = new Set(langs);
      console.log('[Service Worker] Loaded cached languages:', Array.from(cachedLanguages));
    }
  } catch (error) {
    console.log('[Service Worker] No PWA installation flag found');
  }
}

// Function to mark PWA as installed
async function markPWAAsInstalled() {
  try {
    const cache = await caches.open(CACHE_NAME);
    await cache.put('/pwa-installed-flag', new Response('installed', {
      headers: { 'Content-Type': 'text/plain' }
    }));
    isPWAInstalled = true;
    console.log('[Service Worker] PWA marked as installed');
  } catch (error) {
    console.error('[Service Worker] Failed to mark PWA as installed:', error);
  }
}

// Function to save cached languages list
async function saveCachedLanguagesList() {
  try {
    const cache = await caches.open(CACHE_NAME);
    await cache.put('/cached-languages-list', new Response(JSON.stringify(Array.from(cachedLanguages)), {
      headers: { 'Content-Type': 'application/json' }
    }));
    console.log('[Service Worker] Saved cached languages list:', Array.from(cachedLanguages));
  } catch (error) {
    console.error('[Service Worker] Failed to save cached languages list:', error);
  }
}

// Supported languages
const SUPPORTED_LANGUAGES = [
  'en', 'fr', 'es', 'de', 'it', 'pt', 'nl', 'pl', 'uk', 'tr', 'ru', 'ar', 'he', 'fa', 'hi', 'bn',
  'te', 'ta', 'mr', 'gu', 'kn', 'ml', 'pa', 'ur', 'id', 'ms', 'th', 'vi', 'km', 'my', 'zh', 'ja',
  'ko', 'el', 'bg', 'cs', 'sk', 'hu', 'ro', 'hr', 'sr', 'bs', 'sl', 'mk', 'et', 'lv', 'lt', 'da',
  'fi', 'nb', 'sv', 'ca', 'gl', 'eu', 'af', 'sw', 'am', 'ka', 'hy', 'az', 'uz', 'kk', 'tg', 'tk', 'ky'
];

// Available routes
const AVAILABLE_ROUTES = [
  'timer', 'countdown', 'world-clock', 'intervals', 'pomodoro', 'todo',
  'time-tracking', 'meeting-timer', 'time-billing', 'exercise-templates', 'workout-intervals'
];

// Route translations (complete version for service worker)
const ROUTE_TRANSLATIONS = {
  timer: {
    fr: "chronometre", es: "temporizador", de: "stoppuhr", it: "cronometro", pt: "cronometro",
    nl: "timer", pl: "stoper", uk: "таймер", tr: "zamanlayici", ru: "таймер", ar: "مؤقت",
    he: "טיימר", fa: "تایمر", hi: "टाइमर", bn: "টাইমার", te: "టైమర్", ta: "டைமர்",
    mr: "टायमर", gu: "ટાઇમર", kn: "ಟೈಮರ್", ml: "ടൈമർ", pa: "ਟਾਈਮਰ", ur: "ٹائمر",
    id: "pengatur-waktu", ms: "pemasa", th: "นาฬิกาจับเวลา", vi: "dong-ho-bam-gio",
    km: "នាឡិកាកំណត់ម៉ោង", my: "အချိန်တိုင်း", zh: "计时器", ja: "タイマー", ko: "타이머",
    el: "χρονομετρο", bg: "таймер", cs: "casovac", sk: "casovac", hu: "idozito", ro: "cronometru",
    hr: "mjerac-vremena", sr: "тајмер", bs: "tajmer", sl: "casovnik", mk: "тајмер", et: "taimer",
    lv: "taimers", lt: "laikmatis", da: "timer", fi: "ajastin", nb: "tidtaker", sv: "timer",
    ca: "temporitzador", gl: "temporizador", eu: "kronometroa", af: "tydhouer", sw: "kipima-muda",
    am: "ሰዓት-መቁጠሪያ", ka: "ტაიმერი", hy: "ժամանակաչափ", az: "taymer", uz: "taymer",
    kk: "таймер", tg: "вақтсанҷ", tk: "wagt-olcujy", ky: "убакыт-олчогуч"
  },
  countdown: {
    fr: "compte-a-rebours", es: "cuenta-regresiva", de: "countdown", it: "conto-alla-rovescia", pt: "contagem-regressiva",
    nl: "aftelling", pl: "odliczanie", uk: "зворотний-відлік", tr: "geri-sayim", ru: "обратный-отсчет", ar: "عد-تنازلي",
    he: "ספירה-לאחור", fa: "شمارش-معکوس", hi: "उल्टी-गिनती", bn: "উল্টো-গণনা", te: "వెనుకకు-లెక్కింపు",
    ta: "பின்னோக்கி-எண்ணிக்கை", mr: "उलट-मोजणी", gu: "ઉલટી-ગણતરી", kn: "ಹಿಂದಿನ-ಎಣಿಕೆ", ml: "പിന്നോട്ട്-എണ്ണൽ",
    pa: "ਉਲਟ-ਗਿਣਤੀ", ur: "الٹ-گنتی", id: "hitung-mundur", ms: "kira-undur", th: "นับถอยหลัง",
    vi: "dem-nguoc", km: "រាប់ថយក្រោយ", my: "နောက်ပြန်ရေတွက်", zh: "倒计时", ja: "カウントダウン", ko: "카운트다운",
    el: "αντίστροφη-μέτρηση", bg: "обратно-броене", cs: "odpocitavani", sk: "odpocitavanie", hu: "visszaszamlalas",
    ro: "numaratoare-inversa", hr: "odbrojavanje", sr: "одбројавање", bs: "odbrojavanje", sl: "odštevanje",
    mk: "одбројување", et: "tagasiloendus", lv: "atpakalskaitisana", lt: "atgalinis-skaiciavimas",
    da: "nedtaelling", fi: "laskuri", nb: "nedtelling", sv: "nedrakning", ca: "compte-enrere",
    gl: "conta-atras", eu: "atzera-kontaketa", af: "aftelling", sw: "uhesabu-nyuma", am: "ወደ-ኋላ-መቁጠር",
    ka: "უკან-დათვლა", hy: "հակառակ-հաշվարկ", az: "geri-sayim", uz: "teskari-hisob", kk: "кері-санау",
    tg: "ҳисоби-баръакс", tk: "yzyna-hasaplama", ky: "тескери-эсептөө"
  },
  "world-clock": {
    fr: "horloge-mondiale", es: "reloj-mundial", de: "weltzeituhr", it: "orologio-mondiale", pt: "relogio-mundial",
    nl: "wereldklok", pl: "zegar-swiatowy", uk: "світовий-годинник", tr: "dunya-saati", ru: "мировые-часы", ar: "ساعة-عالمية",
    he: "שעון-עולמי", fa: "ساعت-جهانی", hi: "विश्व-घड़ी", bn: "বিশ্ব-ঘড়ি", te: "ప్రపంచ-గడియారం", ta: "உலக-கடிகாரம்",
    mr: "जागतिक-घड्याळ", gu: "વિશ્વ-ઘડિયાળ", kn: "ವಿಶ್ವ-ಗಡಿಯಾರ", ml: "ലോക-ഘടികാരം", pa: "ਵਿਸ਼ਵ-ਘੜੀ", ur: "عالمی-گھڑی",
    id: "jam-dunia", ms: "jam-dunia", th: "นาฬิกาโลก", vi: "dong-ho-the-gioi", km: "នាឡិកាពិភពលោក",
    my: "ကမ္ဘာ့နာရီ", zh: "世界时钟", ja: "世界時計", ko: "세계시계", el: "παγκοσμιο-ρολοι",
    bg: "световен-часовник", cs: "svetove-hodiny", sk: "svetovy-cas", hu: "vilagora", ro: "ceas-mondial",
    hr: "svjetski-sat", sr: "светски-сат", bs: "svjetski-sat", sl: "svetovna-ura", mk: "светски-часовник",
    et: "maailmakell", lv: "pasaules-pulkstenis", lt: "pasaulio-laikrodis", da: "verdensur", fi: "maailmankello",
    nb: "verdensklokke", sv: "varldsklocka", ca: "rellotge-mundial", gl: "reloxo-mundial", eu: "munduko-erlojua",
    af: "wereldhorlosie", sw: "saa-ya-dunia", am: "የዓለም-ሰዓት", ka: "მსოფლიო-საათი", hy: "աշխարհի-ժամացույց",
    az: "dunya-saati", uz: "dunyo-soati", kk: "әлем-сағаты", tg: "соати-ҷаҳон", tk: "dunya-sagady", ky: "дүйнө-саатчы"
  },
  intervals: {
    fr: "intervalles", es: "intervalos", de: "intervalle", it: "intervalli", pt: "intervalos",
    nl: "intervallen", pl: "interwaly", uk: "інтервали", tr: "araliklarr", ru: "интервалы", ar: "فترات",
    he: "מרווחים", fa: "بازه‌ها", hi: "अंतराल", bn: "ব্যবধান", te: "అంతరాలు", ta: "இடைவெளிகள்",
    mr: "मध्यांतर", gu: "અંતરાલ", kn: "ಮಧ್ಯಂತರಗಳು", ml: "ഇടവേളകൾ", pa: "ਅੰਤਰਾਲ", ur: "وقفے",
    id: "interval", ms: "selang", th: "ช่วงเวลา", vi: "khoang-thoi-gian", km: "ចន្លោះពេល",
    my: "အချိန်ကွာ", zh: "间隔", ja: "インターバル", ko: "인터벌", el: "διαστηματα",
    bg: "интервали", cs: "intervaly", sk: "intervaly", hu: "intervallumok", ro: "intervale",
    hr: "intervali", sr: "интервали", bs: "intervali", sl: "intervali", mk: "интервали",
    et: "intervallid", lv: "intervali", lt: "intervalai", da: "intervaller", fi: "valiajat",
    nb: "intervaller", sv: "intervall", ca: "intervals", gl: "intervalos", eu: "tarteak",
    af: "intervalle", sw: "vipindi", am: "ክፍተቶች", ka: "ინტერვალები", hy: "ընդմիջումներ",
    az: "intervallar", uz: "intervallar", kk: "интервалдар", tg: "фосилаҳо", tk: "aralyklar", ky: "интервалдар"
  },
  pomodoro: {
    fr: "pomodoro", es: "pomodoro", de: "pomodoro", it: "pomodoro", pt: "pomodoro",
    nl: "pomodoro", pl: "pomodoro", uk: "помодоро", tr: "pomodoro", ru: "помодоро", ar: "بومودورو",
    he: "פומודורו", fa: "پومودورو", hi: "पोमोडोरो", bn: "পোমোডোরো", te: "పొమోడోరో", ta: "பொமோடோரோ",
    mr: "पोमोडोरो", gu: "પોમોડોરો", kn: "ಪೊಮೊಡೊರೊ", ml: "പൊമൊഡോറോ", pa: "ਪੋਮੋਡੋਰੋ", ur: "پوموڈورو",
    id: "pomodoro", ms: "pomodoro", th: "โปโมโดโร", vi: "pomodoro", km: "ប៉ូម៉ូដូរ៉ូ",
    my: "ပိုမိုဒိုရို", zh: "番茄钟", ja: "ポモドーロ", ko: "뽀모도로", el: "ποmodoro",
    bg: "помодоро", cs: "pomodoro", sk: "pomodoro", hu: "pomodoro", ro: "pomodoro",
    hr: "pomodoro", sr: "помодоро", bs: "pomodoro", sl: "pomodoro", mk: "помодоро",
    et: "pomodoro", lv: "pomodoro", lt: "pomodoro", da: "pomodoro", fi: "pomodoro",
    nb: "pomodoro", sv: "pomodoro", ca: "pomodoro", gl: "pomodoro", eu: "pomodoro",
    af: "pomodoro", sw: "pomodoro", am: "ፖሞዶሮ", ka: "პომოდორო", hy: "պոմոդորո",
    az: "pomodoro", uz: "pomodoro", kk: "помодоро", tg: "помодоро", tk: "pomodoro", ky: "помодоро"
  },
  todo: {
    fr: "taches", es: "tareas", de: "aufgaben", it: "compiti", pt: "tarefas",
    nl: "taken", pl: "zadania", uk: "завдання", tr: "gorevler", ru: "задачи", ar: "مهام",
    he: "משימות", fa: "وظایف", hi: "कार्य", bn: "কাজ", te: "పనులు", ta: "பணிகள்",
    mr: "कार्ये", gu: "કાર્યો", kn: "ಕಾರ್ಯಗಳು", ml: "ജോലികൾ", pa: "ਕੰਮ", ur: "کام",
    id: "tugas", ms: "tugasan", th: "งาน", vi: "cong-viec", km: "ការងារ",
    my: "အလုပ်များ", zh: "待办事项", ja: "タスク", ko: "할일", el: "εργασιες",
    bg: "задачи", cs: "ukoly", sk: "ulohy", hu: "feladatok", ro: "sarcini",
    hr: "zadaci", sr: "задаци", bs: "zadaci", sl: "naloge", mk: "задачи",
    et: "ulesanded", lv: "uzdevumi", lt: "uzduotys", da: "opgaver", fi: "tehtavat",
    nb: "oppgaver", sv: "uppgifter", ca: "tasques", gl: "tarefas", eu: "zereginak",
    af: "take", sw: "kazi", am: "ስራዎች", ka: "დავალებები", hy: "առաջադրանքներ",
    az: "tapshiriqlar", uz: "vazifalar", kk: "тапсырмалар", tg: "вазифаҳо", tk: "meseleleri", ky: "тапшырмалар"
  },
  "time-tracking": {
    fr: "suivi-temps", es: "seguimiento-tiempo", de: "zeiterfassung", it: "tracciamento-tempo", pt: "rastreamento-tempo",
    nl: "tijdregistratie", pl: "sledzenie-czasu", uk: "відстеження-часу", tr: "zaman-takibi", ru: "учет-времени", ar: "تتبع-الوقت",
    he: "מעקב-זמן", fa: "ردیابی-زمان", hi: "समय-ट्रैकिंग", bn: "সময়-ট্র্যাকিং", te: "సమయ-ట్రాకింగ్", ta: "நேர-கண்காணிப்பு",
    mr: "वेळ-ट्रॅकिंग", gu: "સમય-ટ્રેકિંગ", kn: "ಸಮಯ-ಟ್ರ್ಯಾಕಿಂಗ್", ml: "സമയ-ട്രാക്കിംഗ്", pa: "ਸਮਾਂ-ਟਰੈਕਿੰਗ", ur: "وقت-ٹریکنگ",
    id: "pelacakan-waktu", ms: "penjejakan-masa", th: "การติดตามเวลา", vi: "theo-doi-thoi-gian", km: "ការតាមដានពេលវេលា",
    my: "အချိန်ခြေရာခံ", zh: "时间跟踪", ja: "時間追跡", ko: "시간추적", el: "παρακολουθηση-χρονου",
    bg: "проследяване-време", cs: "sledovani-casu", sk: "sledovanie-casu", hu: "ido-kovetes", ro: "urmarire-timp",
    hr: "pracenje-vremena", sr: "праћење-времена", bs: "pracenje-vremena", sl: "sledenje-casa", mk: "следење-време",
    et: "aja-jalgmine", lv: "laika-izsekosana", lt: "laiko-sekimas", da: "tidssporing", fi: "ajan-seuranta",
    nb: "tidssporing", sv: "tidssparing", ca: "seguiment-temps", gl: "seguimento-tempo", eu: "denbora-jarraipena",
    af: "tyd-opsporing", sw: "ufuatiliaji-muda", am: "የጊዜ-መከታተል", ka: "დროის-თვალყურის-დევნება", hy: "ժամանակի-հետևում",
    az: "vaxt-izleme", uz: "vaqt-kuzatuv", kk: "уақытты-бақылау", tg: "пайгирии-вақт", tk: "wagt-yzarlama", ky: "убакытты-көзөмөлдөө"
  },
  "meeting-timer": {
    fr: "minuteur-reunion", es: "temporizador-reunion", de: "besprechungs-timer", it: "timer-riunione", pt: "cronometro-reuniao",
    nl: "vergader-timer", pl: "timer-spotkania", uk: "таймер-зустрічі", tr: "toplanti-zamanlayici", ru: "таймер-встречи", ar: "مؤقت-اجتماع",
    he: "טיימר-פגישה", fa: "تایمر-جلسه", hi: "मीटिंग-टाइमर", bn: "মিটিং-টাইমার", te: "మీటింగ్-టైమర్", ta: "கூட்ட-டைமர்",
    mr: "मीटिंग-टायमर", gu: "મીટિંગ-ટાઇમર", kn: "ಸಭೆ-ಟೈಮರ್", ml: "മീറ്റിംഗ്-ടൈമർ", pa: "ਮੀਟਿੰਗ-ਟਾਈਮਰ", ur: "میٹنگ-ٹائمر",
    id: "timer-rapat", ms: "pemasa-mesyuarat", th: "ตัวจับเวลาประชุม", vi: "dong-ho-hop", km: "កម្មវិធីកំណត់ម៉ោងប្រជុំ",
    my: "အစည်းအဝေးအချိန်တိုင်း", zh: "会议计时器", ja: "会議タイマー", ko: "회의타이머", el: "χρονομετρο-συναντησης",
    bg: "таймер-среща", cs: "casovac-schuzky", sk: "casovac-stretnutia", hu: "megbeszeles-idozito", ro: "cronometru-intalnire",
    hr: "mjerac-sastanka", sr: "тајмер-састанка", bs: "tajmer-sastanka", sl: "casovnik-sestanka", mk: "тајмер-состанок",
    et: "koosoleku-taimer", lv: "sapulces-taimers", lt: "susitikimo-laikmatis", da: "mode-timer", fi: "kokous-ajastin",
    nb: "mote-tidtaker", sv: "motes-timer", ca: "temporitzador-reunio", gl: "temporizador-reunion", eu: "bilera-kronometroa",
    af: "vergadering-tydhouer", sw: "kipima-mkutano", am: "የስብሰባ-ሰዓት-መቁጠሪያ", ka: "შეხვედრის-ტაიმერი", hy: "հանդիպման-ժամանակաչափ",
    az: "gorusme-taymeri", uz: "yigʻilish-taymeri", kk: "кездесу-таймері", tg: "вақтсанҷи-ҷаласа", tk: "dususlyk-wagt-olcujy", ky: "жолугушуу-таймери"
  },
  "time-billing": {
    fr: "facturation-temps", es: "facturacion-tiempo", de: "zeit-abrechnung", it: "fatturazione-tempo", pt: "faturamento-tempo",
    nl: "tijd-facturering", pl: "rozliczanie-czasu", uk: "виставлення-рахунків", tr: "zaman-faturalandirma", ru: "выставление-счетов", ar: "فوترة-الوقت",
    he: "חיוב-זמן", fa: "صورتحساب-زمان", hi: "समय-बिलिंग", bn: "সময়-বিলিং", te: "సమయ-బిల్లింగ్", ta: "நேர-பில்லிங்",
    mr: "वेळ-बिलिंग", gu: "સમય-બિલિંગ", kn: "ಸಮಯ-ಬಿಲ್ಲಿಂಗ್", ml: "സമയ-ബില്ലിംഗ്", pa: "ਸਮਾਂ-ਬਿਲਿੰਗ", ur: "وقت-بلنگ",
    id: "penagihan-waktu", ms: "pengebilan-masa", th: "การเรียกเก็บเงินตามเวลา", vi: "tinh-phi-theo-thoi-gian", km: "ការគិតថ្លៃតាមពេលវេលា",
    my: "အချိန်အလိုက်ငွေတောင်း", zh: "时间计费", ja: "時間請求", ko: "시간청구", el: "χρεωση-χρονου",
    bg: "таксуване-време", cs: "ucetni-cas", sk: "uctovanie-casu", hu: "ido-szamlazas", ro: "facturare-timp",
    hr: "naplata-vremena", sr: "наплата-времена", bs: "naplata-vremena", sl: "zaracunavanje-casa", mk: "наплата-време",
    et: "aja-arvestus", lv: "laika-apskaite", lt: "laiko-apskaita", da: "tids-fakturering", fi: "ajan-laskutus",
    nb: "tids-fakturering", sv: "tids-fakturering", ca: "facturacio-temps", gl: "facturacion-tempo", eu: "denbora-fakturaketa",
    af: "tyd-fakturering", sw: "malipo-ya-muda", am: "የጊዜ-ክፍያ", ka: "დროის-ანგარიშგება", hy: "ժամանակի-հաշվարկ",
    az: "vaxt-hesablamasi", uz: "vaqt-hisoblash", kk: "уақыт-есептеу", tg: "ҳисоби-вақт", tk: "wagt-hasaplamalary", ky: "убакыт-эсептөө"
  },
  "exercise-templates": {
    fr: "modeles-exercices", es: "plantillas-ejercicios", de: "uebungs-vorlagen", it: "modelli-esercizi", pt: "modelos-exercicios",
    nl: "oefening-sjablonen", pl: "szablony-cwiczen", uk: "шаблони-вправ", tr: "egzersiz-sablonlari", ru: "шаблоны-упражнений", ar: "قوالب-التمارين",
    he: "תבניות-תרגילים", fa: "قالب-تمرینات", hi: "व्यायाम-टेम्प्लेट", bn: "ব্যায়াম-টেমপ্লেট", te: "వ్యాయామ-టెంప్లేట్లు", ta: "உடற்பயிற்சி-வார்ப்புருக்கள்",
    mr: "व्यायाम-टेम्प्लेट", gu: "કસરત-ટેમ્પ્લેટ", kn: "ವ್ಯಾಯಾಮ-ಟೆಂಪ್ಲೇಟ್", ml: "വ്യായാമ-ടെംപ്ലേറ്റുകൾ", pa: "ਕਸਰਤ-ਟੈਂਪਲੇਟ", ur: "ورزش-ٹیمپلیٹ",
    id: "template-latihan", ms: "templat-senaman", th: "แม่แบบการออกกำลังกาย", vi: "mau-bai-tap", km: "គំរូលំហាត់ប្រាណ",
    my: "လေ့ကျင့်ခန်းပုံစံများ", zh: "运动模板", ja: "エクササイズテンプレート", ko: "운동템플릿", el: "προτυπα-ασκησεων",
    bg: "шаблони-упражнения", cs: "sablony-cviceni", sk: "sablony-cviceni", hu: "gyakorlat-sablonok", ro: "sabloane-exercitii",
    hr: "predlosci-vjezbi", sr: "шаблони-вежби", bs: "predlosci-vjezbi", sl: "predloge-vaj", mk: "шаблони-вежби",
    et: "harjutuste-mallid", lv: "vingrinajumu-veidnes", lt: "pratimu-sablonai", da: "ovelses-skabeloner", fi: "harjoitus-mallit",
    nb: "ovelse-maler", sv: "ovnings-mallar", ca: "plantilles-exercicis", gl: "modelos-exercicios", eu: "ariketa-txantiloiak",
    af: "oefening-sjablone", sw: "mifano-ya-mazoezi", am: "የአካል-ብቃት-ቅጦች", ka: "ვარჯიშის-შაბლონები", hy: "վարժանքների-ձևանմուշներ",
    az: "meskq-sablonlari", uz: "mashq-andozalari", kk: "жаттығу-үлгілері", tg: "қолибҳои-машқ", tk: "mesgleniş-nusgalary", ky: "көнүгүү-үлгүлөрү"
  },
  "workout-intervals": {
    fr: "intervalles-entrainement", es: "intervalos-entrenamiento", de: "trainings-intervalle", it: "intervalli-allenamento", pt: "intervalos-treino",
    nl: "training-intervallen", pl: "interwaly-treningowe", uk: "інтервали-тренування", tr: "antrenman-araliklari", ru: "интервалы-тренировки", ar: "فترات-التدريب",
    he: "מרווחי-אימון", fa: "بازه‌های-تمرین", hi: "वर्कआउट-अंतराल", bn: "ওয়ার্কআউট-ইন্টারভাল", te: "వర్కౌట్-ఇంటర్వల్స్", ta: "உடற்பயிற்சி-இடைவெளிகள்",
    mr: "वर्कआउट-मध्यांतर", gu: "વર્કઆઉટ-અંતરાલ", kn: "ವರ್ಕೌಟ್-ಮಧ್ಯಂತರಗಳು", ml: "വർക്കൗട്ട്-ഇടവേളകൾ", pa: "ਵਰਕਆਉਟ-ਅੰਤਰਾਲ", ur: "ورکآؤٹ-وقفے",
    id: "interval-latihan", ms: "selang-senaman", th: "ช่วงการออกกำลังกาย", vi: "khoang-tap-luyen", km: "ចន្លោះលំហាត់ប្រាណ",
    my: "လေ့ကျင့်ခန်းအချိန်ကွာ", zh: "锻炼间隔", ja: "ワークアウトインターバル", ko: "운동인터벌", el: "διαστηματα-προπονησης",
    bg: "интервали-тренировка", cs: "intervaly-treningu", sk: "intervaly-treningu", hu: "edzes-intervallumok", ro: "intervale-antrenament",
    hr: "intervali-treninga", sr: "интервали-тренинга", bs: "intervali-treninga", sl: "intervali-treninga", mk: "интервали-тренинг",
    et: "treeningu-intervallid", lv: "treninu-intervali", lt: "treniruociu-intervalai", da: "tranings-intervaller", fi: "harjoitus-valiajat",
    nb: "trenings-intervaller", sv: "tranings-intervall", ca: "intervals-entrenament", gl: "intervalos-adestramento", eu: "entrenamendu-tarteak",
    af: "oefening-intervalle", sw: "vipindi-vya-mazoezi", am: "የአካል-ብቃት-ክፍተቶች", ka: "ვარჯიშის-ინტერვალები", hy: "մարզանքի-ընդմիջումներ",
    az: "meskq-intervallari", uz: "mashq-intervallari", kk: "жаттығу-интервалдары", tg: "фосилаҳои-машқ", tk: "mesgleniş-aralyklary", ky: "көнүгүү-интервалдары"
  }
};

// Function to get translated route
function getTranslatedRoute(lang, route) {
  if (ROUTE_TRANSLATIONS[route] && ROUTE_TRANSLATIONS[route][lang]) {
    return ROUTE_TRANSLATIONS[route][lang];
  }
  return route;
}

// Function to get original route from translated route
function getOriginalRoute(lang, translatedRoute) {
  for (const [originalRoute, translations] of Object.entries(ROUTE_TRANSLATIONS)) {
    if (translations[lang] === translatedRoute) {
      return originalRoute;
    }
  }
  return translatedRoute;
}

// Function to track installation
async function trackInstallation() {
  try {
    await fetch('/api/install-counter', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        timestamp: new Date().toISOString(),
        source: 'service-worker'
      })
    });
    console.log('[Service Worker] Installation tracked successfully');
  } catch (error) {
    console.error('[Service Worker] Failed to track installation:', error);
  }
}

// Install event - precache only essential assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker v9...');
  console.log('[Service Worker] PRECACHE_ASSETS:', PRECACHE_ASSETS);

  // Skip waiting to ensure the new service worker activates immediately
  self.skipWaiting();

  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(async (cache) => {
        console.log(`[Service Worker] Opened cache: ${CACHE_NAME}`);
        console.log('[Service Worker] Precaching essential assets only');

        try {
          // Cache only core assets during install
          await cache.addAll(PRECACHE_ASSETS);
          console.log('[Service Worker] Successfully cached all precache assets');
        } catch (error) {
          console.error('[Service Worker] Failed to cache some precache assets:', error);
          // Try to cache them individually
          for (const asset of PRECACHE_ASSETS) {
            try {
              const response = await fetch(asset);
              if (response.ok) {
                await cache.put(asset, response);
                console.log(`[Service Worker] Individually cached: ${asset}`);
              } else {
                console.warn(`[Service Worker] Failed to fetch ${asset}: ${response.status}`);
              }
            } catch (err) {
              console.warn(`[Service Worker] Error caching ${asset}:`, err);
            }
          }
        }

        // Check if PWA was previously installed
        await checkPWAInstallation();

        console.log('[Service Worker] Installation completed');
      })
      .catch((error) => {
        console.error('[Service Worker] Cache opening failed:', error);
      })
  );
});

// Helper function to determine if a request is an API call
const isApiRequest = (url) => {
  return url.pathname.startsWith('/api/');
};

// Helper function to determine if a request is for a static asset
const isStaticAsset = (url) => {
  return (
    url.pathname.startsWith('/_next/static/') ||
    url.pathname.startsWith('/icons/') ||
    url.pathname.startsWith('/sound/') ||
    url.pathname.startsWith('/workers/') ||
    url.pathname.startsWith('/screenshots/') ||
    url.pathname.endsWith('.png') ||
    url.pathname.endsWith('.jpg') ||
    url.pathname.endsWith('.jpeg') ||
    url.pathname.endsWith('.svg') ||
    url.pathname.endsWith('.json') ||
    url.pathname.endsWith('.js') ||
    url.pathname.endsWith('.css') ||
    url.pathname.endsWith('.woff') ||
    url.pathname.endsWith('.woff2') ||
    url.pathname.endsWith('.ttf') ||
    url.pathname.endsWith('.ico') ||
    url.pathname.endsWith('.mp3') ||
    url.pathname.endsWith('.wav') ||
    url.pathname.endsWith('.ogg') ||
    url.pathname === '/favicon.ico' ||
    url.pathname === '/logo.png' ||
    url.pathname === '/robots.txt' ||
    url.pathname === '/sitemap.xml'
  );
};

// Helper function to determine if a request is for an HTML page
const isHtmlPage = (request) => {
  return (
    request.mode === 'navigate' ||
    (request.method === 'GET' &&
     request.headers.get('accept') &&
     request.headers.get('accept').includes('text/html'))
  );
};

// Helper function to find cached page with fallback strategy
async function findCachedPageWithFallback(requestUrl) {
  const url = new URL(requestUrl);
  const pathSegments = url.pathname.split('/').filter(Boolean);

  console.log(`[Service Worker] Looking for cached page: ${requestUrl}`);

  // Try exact match first
  let cachedResponse = await caches.match(requestUrl);
  if (cachedResponse) {
    console.log(`[Service Worker] Found exact match: ${requestUrl}`);
    return cachedResponse;
  }

  if (pathSegments.length >= 1) {
    const lang = pathSegments[0];
    const route = pathSegments[1];

    // If it's a language home page, try English home page only if it's cached
    if (pathSegments.length === 1 && SUPPORTED_LANGUAGES.includes(lang) && lang !== 'en') {
      // Only try English if it's actually being tracked
      if (cachedLanguages.has('en')) {
        cachedResponse = await caches.match('/en');
        if (cachedResponse) {
          console.log(`[Service Worker] Found English home fallback for ${requestUrl}`);
          return cachedResponse;
        }
      }
    }

    // If it's a tool page, try different fallback strategies
    if (pathSegments.length >= 2 && SUPPORTED_LANGUAGES.includes(lang) && route) {
      // Strategy 1: Check if this is a translated route and try the original route
      const originalRoute = getOriginalRoute(lang, route);
      if (originalRoute !== route) {
        const originalPath = `/${lang}/${originalRoute}`;
        cachedResponse = await caches.match(originalPath);
        if (cachedResponse) {
          console.log(`[Service Worker] Found original route: ${originalPath} for ${requestUrl}`);
          return cachedResponse;
        }
      }

      // Strategy 2: Try the same original route in English (only if English is tracked)
      if (lang !== 'en' && cachedLanguages.has('en')) {
        const englishPath = `/en/${originalRoute}`;
        cachedResponse = await caches.match(englishPath);
        if (cachedResponse) {
          console.log(`[Service Worker] Found English fallback: ${englishPath} for ${requestUrl}`);
          return cachedResponse;
        }
      }

      // Strategy 3: Try the home page of the same language (only if language is tracked)
      if (cachedLanguages.has(lang)) {
        const langHomePath = `/${lang}`;
        cachedResponse = await caches.match(langHomePath);
        if (cachedResponse) {
          console.log(`[Service Worker] Found language home fallback: ${langHomePath} for ${requestUrl}`);
          return cachedResponse;
        }
      }

      // Strategy 4: Try English home page (only if English is tracked)
      if (lang !== 'en' && cachedLanguages.has('en')) {
        cachedResponse = await caches.match('/en');
        if (cachedResponse) {
          console.log(`[Service Worker] Found English home fallback for ${requestUrl}`);
          return cachedResponse;
        }
      }
    }
  }

  // Final fallback: offline page
  console.log(`[Service Worker] Using offline page fallback for ${requestUrl}`);
  const offlinePage = await caches.match('/offline.html');
  if (offlinePage) {
    return offlinePage;
  }

  // Last resort: create a simple offline response
  return new Response(`
    <!DOCTYPE html>
    <html>
    <head><title>Offline - Timer Kit</title></head>
    <body>
      <h1>You're Offline</h1>
      <p>This page is not available offline. Please check your connection.</p>
      <button onclick="window.location.reload()">Try Again</button>
    </body>
    </html>
  `, {
    status: 200,
    headers: { 'Content-Type': 'text/html' }
  });
}

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);

  // Skip cross-origin requests
  if (url.origin !== self.location.origin) {
    return;
  }

  // Skip requests for extensions or browser-specific URLs
  if (url.protocol !== 'http:' && url.protocol !== 'https:') {
    return;
  }

  // Log only important requests
  if (url.pathname.includes('/api/') || url.pathname.endsWith('.html')) {
    console.log(`[Service Worker] Handling request: ${event.request.method} ${url.pathname}`);
  }

  // Strategy for static assets: Cache First, falling back to network
  if (isStaticAsset(url)) {
    event.respondWith(
      caches.match(event.request).then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(event.request)
          .then((response) => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response as it can only be consumed once
            const responseToCache = response.clone();

            // Only cache static assets if PWA is installed
            if (isPWAInstalled) {
              caches.open(CACHE_NAME).then((cache) => {
                cache.put(event.request, responseToCache);
              });
            }

            return response;
          })
          .catch(() => {
            // If both cache and network fail, return a fallback
            return new Response('Network error occurred', {
              status: 408,
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
    );
    return;
  }

  // Strategy for HTML pages: Cache First for offline reliability
  if (isHtmlPage(event.request)) {
    event.respondWith(
      caches.match(event.request)
        .then(async (cachedResponse) => {
          if (cachedResponse) {
            console.log(`[Service Worker] Serving from cache: ${url.pathname}`);
            return cachedResponse;
          }

          // Try network first, then fallback
          try {
            const response = await fetch(event.request);

            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response for caching
            const responseToCache = response.clone();

            // Only cache if PWA is installed and language is being tracked
            if (isPWAInstalled) {
              const pathSegments = url.pathname.split('/').filter(Boolean);
              const lang = pathSegments[0];

              // Only cache if this language is being tracked
              if (lang && cachedLanguages.has(lang)) {
                caches.open(CACHE_NAME).then((cache) => {
                  cache.put(event.request, responseToCache);

                  // Also cache translated route if applicable
                  if (pathSegments.length === 2) {
                    const route = pathSegments[1];
                    const translatedRoute = getTranslatedRoute(lang, route);

                    if (translatedRoute !== route) {
                      const translatedUrl = `/${lang}/${translatedRoute}`;
                      cache.put(translatedUrl, responseToCache.clone());
                      console.log(`[Service Worker] Also cached translated route: ${translatedUrl}`);
                    }
                  }
                });
              }
            }

            return response;
          } catch (error) {
            console.log(`[Service Worker] Network failed for ${url.pathname}, using fallback`);
            return await findCachedPageWithFallback(event.request.url);
          }
        })
    );
    return;
  }

  // Default strategy for other requests: Network Only with offline fallback
  event.respondWith(
    fetch(event.request)
      .catch(() => {
        return caches.match(event.request).then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }

          // If no cached version, return a simple offline message
          if (isApiRequest(url)) {
            return new Response(JSON.stringify({ error: 'You are offline' }), {
              status: 503,
              headers: { 'Content-Type': 'application/json' }
            });
          }

          return new Response('You are offline', {
            status: 503,
            headers: { 'Content-Type': 'text/plain' }
          });
        });
      })
  );
});

// Handle app installation
self.addEventListener('appinstalled', () => {
  console.log('[Service Worker] App was installed');
  trackInstallation();

  // Mark PWA as installed (no automatic caching)
  markPWAAsInstalled().then(() => {
    console.log('[Service Worker] PWA installation marked, caching will be done on demand');
  });
});

// Function to cache all pages for a specific language (only if PWA is installed)
async function cacheLanguagePages(lang) {
  if (!isPWAInstalled) {
    console.log(`[Service Worker] Skipping language caching for ${lang} - PWA not installed`);
    return;
  }

  // Check if language is already cached
  if (cachedLanguages.has(lang)) {
    console.log(`[Service Worker] Language ${lang} already cached, skipping`);
    return;
  }

  console.log(`[Service Worker] Starting to cache language: ${lang}`);

  try {
    const cache = await caches.open(CACHE_NAME);

    // Cache home page
    try {
      const homeUrl = `/${lang}`;
      const homeResponse = await fetch(homeUrl);
      if (homeResponse.ok) {
        await cache.put(homeUrl, homeResponse);
        console.log(`[Service Worker] Cached home page: ${homeUrl}`);
      }
    } catch (error) {
      console.warn(`[Service Worker] Failed to cache home page for ${lang}:`, error);
    }

    // Cache all tool pages for this language (original routes only)
    for (const route of AVAILABLE_ROUTES) {
      try {
        const url = `/${lang}/${route}`;
        const response = await fetch(url);
        if (response.ok) {
          await cache.put(url, response);
          console.log(`[Service Worker] Cached page: ${url}`);

          // Also cache the translated route URL if it's different
          const translatedRoute = getTranslatedRoute(lang, route);
          if (translatedRoute !== route) {
            const translatedUrl = `/${lang}/${translatedRoute}`;
            // Cache the same response under the translated URL
            await cache.put(translatedUrl, response.clone());
            console.log(`[Service Worker] Cached translated route: ${translatedUrl}`);
          }
        }
      } catch (error) {
        console.warn(`[Service Worker] Failed to cache ${url}:`, error);
      }
    }

    // Mark language as cached
    cachedLanguages.add(lang);
    await saveCachedLanguagesList();
    console.log(`[Service Worker] Successfully cached all pages for language: ${lang}`);
  } catch (error) {
    console.error(`[Service Worker] Failed to cache pages for language ${lang}:`, error);
  }
}

// Function to cache a specific language on demand
async function cacheLanguageOnDemand(lang) {
  console.log(`[Service Worker] cacheLanguageOnDemand called with lang: ${lang}`);
  console.log(`[Service Worker] isPWAInstalled: ${isPWAInstalled}`);
  console.log(`[Service Worker] SUPPORTED_LANGUAGES includes ${lang}: ${SUPPORTED_LANGUAGES.includes(lang)}`);

  if (!isPWAInstalled) {
    console.log(`[Service Worker] Skipping on-demand caching for ${lang} - PWA not installed`);
    return;
  }

  if (!SUPPORTED_LANGUAGES.includes(lang)) {
    console.log(`[Service Worker] Language ${lang} not supported, skipping`);
    return;
  }

  console.log(`[Service Worker] Caching language on demand: ${lang}`);
  await cacheLanguagePages(lang);
}

// Handle messages from clients
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  } else if (event.data && event.data.type === 'PWA_INSTALLED') {
    console.log('[Service Worker] Received PWA_INSTALLED message');
    markPWAAsInstalled();
  } else if (event.data && event.data.type === 'CACHE_LANGUAGE') {
    const { lang } = event.data;
    console.log(`[Service Worker] Received request to cache language: ${lang}`);
    cacheLanguageOnDemand(lang);
  } else if (event.data && event.data.type === 'LANGUAGE_CHANGED') {
    const { newLang, oldLang } = event.data;
    console.log(`[Service Worker] Language changed from ${oldLang} to ${newLang}`);
    cacheLanguageOnDemand(newLang);
  } else if (event.data && event.data.type === 'FORCE_CACHE_ALL') {
    console.log('[Service Worker] Received FORCE_CACHE_ALL message');
    forceCacheAllLanguages();
  }
});

// Function to force cache multiple languages
async function forceCacheAllLanguages() {
  console.log('[Service Worker] Force caching all important languages...');

  // Force mark PWA as installed
  isPWAInstalled = true;
  await markPWAAsInstalled();

  // Cache most important languages
  const priorityLanguages = ['fr', 'en', 'es', 'de'];

  for (const lang of priorityLanguages) {
    try {
      console.log(`[Service Worker] Force caching language: ${lang}`);
      await cacheLanguagePages(lang);
      // Small delay between languages
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`[Service Worker] Failed to force cache ${lang}:`, error);
    }
  }

  console.log('[Service Worker] Force cache completed');
}

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');

  // Claim clients to ensure the SW controls all clients immediately
  event.waitUntil(self.clients.claim());

  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] Removing old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );

  // Check if PWA was previously installed
  event.waitUntil(
    checkPWAInstallation().then(() => {
      if (isPWAInstalled) {
        console.log('[Service Worker] PWA was previously installed, ready for on-demand caching');
      } else {
        console.log('[Service Worker] PWA not installed, caching disabled');
      }
    })
  );
});
